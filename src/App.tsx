import {
  motion,
  useMotionValueEvent,
  useScroll,
  useTransform,
} from "framer-motion";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

// Define instruction steps
const instructionSteps = [
  {
    id: 1,
    title: "Step 1: Open the Lid",
    description: "Gently lift the charging case lid to reveal your earbuds",
    frameRange: [1, 20],
    position: { top: "20%", left: "10%" },
  },
  {
    id: 2,
    title: "Step 2: Remove the Earbuds",
    description: "Carefully take out both earbuds from the charging case",
    frameRange: [21, 40],
    position: { top: "40%", left: "15%" },
  },
  {
    id: 3,
    title: "Step 3: Put in Your Ears",
    description:
      "Insert the earbuds into your ears with a gentle twisting motion",
    frameRange: [41, 60],
    position: { top: "60%", left: "10%" },
  },
  {
    id: 4,
    title: "Step 4: Adjust for Comfort",
    description:
      "Ensure a secure and comfortable fit for optimal sound quality",
    frameRange: [61, 86],
    position: { top: "80%", left: "20%" },
  },
];

function App() {
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [currentStep, setCurrentStep] = useState(0);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end end"],
  });

  const images = useMemo(() => {
    const loadedImages: HTMLImageElement[] = [];

    for (let i = 1; i <= 86; i++) {
      const img = new Image();
      img.src = `/images/${i}.webp`;
      loadedImages.push(img);
    }

    return loadedImages;
  }, []);

  const render = useCallback(
    (index: number) => {
      if (images[index - 1] && canvasRef.current) {
        const ctx = canvasRef.current.getContext("2d");
        if (ctx) {
          ctx.clearRect(
            0,
            0,
            canvasRef.current.width,
            canvasRef.current.height
          );
          ctx.drawImage(
            images[index - 1],
            0,
            0,
            canvasRef.current.width,
            canvasRef.current.height
          );
        }
      }
    },
    [images]
  );

  const currentIndex = useTransform(scrollYProgress, [0, 1], [1, 86]);

  useMotionValueEvent(currentIndex, "change", (latest) => {
    const frameIndex = Number(latest.toFixed());
    render(frameIndex);

    // Determine current step based on frame
    const step = instructionSteps.findIndex(
      (step) =>
        frameIndex >= step.frameRange[0] && frameIndex <= step.frameRange[1]
    );
    if (step !== -1 && step !== currentStep) {
      setCurrentStep(step);
    }
  });

  useEffect(() => {
    render(1);
  }, [render]);

  return (
    <div ref={containerRef} style={{ position: "relative" }}>
      {/* Main scroll container */}
      <div
        style={{
          height: "500vh", // 5x viewport height for smooth scrolling
          backgroundColor: "#0a0a0a",
          position: "relative",
        }}
      >
        {/* Fixed video container on the right */}
        <div
          style={{
            position: "fixed",
            right: "5%",
            top: "50%",
            transform: "translateY(-50%)",
            width: "45%",
            maxWidth: "600px",
            zIndex: 10,
            borderRadius: "20px",
            overflow: "hidden",
            boxShadow: "0 20px 60px rgba(0, 0, 0, 0.5)",
          }}
        >
          <canvas
            ref={canvasRef}
            width={600}
            height={600}
            style={{
              width: "100%",
              height: "auto",
              display: "block",
            }}
          />
        </div>

        {/* Instruction steps on the left */}
        <div
          style={{
            position: "relative",
            width: "45%",
            height: "100%",
            paddingLeft: "5%",
            paddingTop: "10vh",
          }}
        >
          {instructionSteps.map((step, index) => (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, x: -50 }}
              animate={{
                opacity: currentStep === index ? 1 : 0.3,
                x: currentStep === index ? 0 : -20,
                scale: currentStep === index ? 1 : 0.95,
              }}
              transition={{
                duration: 0.6,
                ease: "easeOut",
              }}
              style={{
                position: "absolute",
                top: `${20 + index * 20}%`,
                left: "0",
                maxWidth: "400px",
                padding: "30px",
                backgroundColor:
                  currentStep === index
                    ? "rgba(255, 255, 255, 0.95)"
                    : "rgba(255, 255, 255, 0.1)",
                borderRadius: "16px",
                backdropFilter: "blur(10px)",
                border:
                  currentStep === index
                    ? "2px solid #007AFF"
                    : "1px solid rgba(255, 255, 255, 0.2)",
                boxShadow:
                  currentStep === index
                    ? "0 20px 40px rgba(0, 122, 255, 0.3)"
                    : "0 10px 30px rgba(0, 0, 0, 0.2)",
              }}
            >
              <motion.div
                animate={{
                  color: currentStep === index ? "#000" : "#fff",
                }}
                transition={{ duration: 0.3 }}
              >
                <h2
                  style={{
                    margin: "0 0 15px 0",
                    fontSize: "24px",
                    fontWeight: "600",
                    fontFamily:
                      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                  }}
                >
                  {step.title}
                </h2>
                <p
                  style={{
                    margin: 0,
                    fontSize: "16px",
                    lineHeight: "1.5",
                    fontFamily:
                      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                  }}
                >
                  {step.description}
                </p>
              </motion.div>

              {/* Step indicator */}
              <motion.div
                style={{
                  position: "absolute",
                  top: "-10px",
                  right: "-10px",
                  width: "40px",
                  height: "40px",
                  borderRadius: "50%",
                  backgroundColor:
                    currentStep === index
                      ? "#007AFF"
                      : "rgba(255, 255, 255, 0.2)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  color: "#fff",
                  fontWeight: "bold",
                  fontSize: "18px",
                  fontFamily:
                    '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                }}
                animate={{
                  scale: currentStep === index ? 1.1 : 1,
                  backgroundColor:
                    currentStep === index
                      ? "#007AFF"
                      : "rgba(255, 255, 255, 0.2)",
                }}
                transition={{ duration: 0.3 }}
              >
                {step.id}
              </motion.div>
            </motion.div>
          ))}
        </div>

        {/* Progress indicator */}
        <motion.div
          style={{
            position: "fixed",
            bottom: "30px",
            left: "50%",
            transform: "translateX(-50%)",
            display: "flex",
            gap: "10px",
            zIndex: 20,
          }}
        >
          {instructionSteps.map((_, index) => (
            <motion.div
              key={index}
              style={{
                width: "12px",
                height: "12px",
                borderRadius: "50%",
                backgroundColor:
                  currentStep === index
                    ? "#007AFF"
                    : "rgba(255, 255, 255, 0.3)",
              }}
              animate={{
                scale: currentStep === index ? 1.3 : 1,
                backgroundColor:
                  currentStep === index
                    ? "#007AFF"
                    : "rgba(255, 255, 255, 0.3)",
              }}
              transition={{ duration: 0.3 }}
            />
          ))}
        </motion.div>
      </div>
    </div>
  );
}

export default App;
