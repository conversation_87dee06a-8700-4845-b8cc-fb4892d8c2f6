import {
  motion,
  useMotionValueEvent,
  useScroll,
  useTransform,
} from "framer-motion";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

// Define instruction steps
const instructionSteps = [
  {
    id: 1,
    title: "Step 1: Open the Lid",
    description: "Gently lift the charging case lid to reveal your earbuds",
    frameRange: [1, 20],
    position: { top: "20%", left: "10%" },
  },
  {
    id: 2,
    title: "Step 2: Remove the Earbuds",
    description: "Carefully take out both earbuds from the charging case",
    frameRange: [21, 40],
    position: { top: "40%", left: "15%" },
  },
  {
    id: 3,
    title: "Step 3: Put in Your Ears",
    description:
      "Insert the earbuds into your ears with a gentle twisting motion",
    frameRange: [41, 60],
    position: { top: "60%", left: "10%" },
  },
  {
    id: 4,
    title: "Step 4: Adjust for Comfort",
    description:
      "Ensure a secure and comfortable fit for optimal sound quality",
    frameRange: [61, 86],
    position: { top: "80%", left: "20%" },
  },
];

function App() {
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [currentStep, setCurrentStep] = useState(0);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end end"],
  });

  const images = useMemo(() => {
    const loadedImages: HTMLImageElement[] = [];

    for (let i = 1; i <= 86; i++) {
      const img = new Image();
      img.src = `/images/${i}.webp`;
      loadedImages.push(img);
    }

    return loadedImages;
  }, []);

  const render = useCallback(
    (index: number) => {
      if (images[index - 1] && canvasRef.current) {
        const ctx = canvasRef.current.getContext("2d");
        if (ctx) {
          ctx.clearRect(
            0,
            0,
            canvasRef.current.width,
            canvasRef.current.height
          );
          ctx.drawImage(
            images[index - 1],
            0,
            0,
            canvasRef.current.width,
            canvasRef.current.height
          );
        }
      }
    },
    [images]
  );

  const currentIndex = useTransform(scrollYProgress, [0, 1], [1, 86]);

  useMotionValueEvent(currentIndex, "change", (latest) => {
    const frameIndex = Number(latest.toFixed());
    render(frameIndex);

    // Determine current step based on frame
    const step = instructionSteps.findIndex(
      (step) =>
        frameIndex >= step.frameRange[0] && frameIndex <= step.frameRange[1]
    );
    if (step !== -1 && step !== currentStep) {
      setCurrentStep(step);
    }
  });

  useEffect(() => {
    render(1);
  }, [render]);

  return (
    <div ref={containerRef} style={{ position: "relative" }}>
      {/* Main scroll container */}
      <div
        style={{
          height: "500vh", // 5x viewport height for smooth scrolling
          backgroundColor: "#0a0a0a",
          position: "relative",
        }}
      >
        {/* Fixed video container on the right */}
        <div
          style={{
            position: "fixed",
            right: "2%",
            top: "50%",
            transform: "translateY(-50%)",
            width: "75%", // 3/4 of screen width
            height: "85vh",
            zIndex: 10,
            borderRadius: "20px",
            overflow: "hidden",
            boxShadow: "0 20px 60px rgba(0, 0, 0, 0.5)",
          }}
        >
          <canvas
            ref={canvasRef}
            width={600}
            height={600}
            style={{
              width: "100%",
              height: "100%",
              display: "block",
              objectFit: "cover", // Cover the container
            }}
          />
        </div>

        {/* Instruction steps on the left */}
        <div
          style={{
            position: "relative",
            width: "20%", // Remaining 1/4 of screen
            height: "100%",
            paddingLeft: "3%",
            marginLeft: "2%", // Added margin as requested
            paddingTop: "10vh",
          }}
        >
          {instructionSteps.map((step, index) => (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, x: -30 }}
              animate={{
                opacity: currentStep === index ? 1 : 0.4,
                x: currentStep === index ? 0 : -15,
                scale: currentStep === index ? 1 : 0.96,
              }}
              transition={{
                duration: 0.5,
                ease: "easeOut",
              }}
              style={{
                position: "absolute",
                top: `${15 + index * 18}%`, // More compact spacing
                left: "0",
                width: "100%",
                maxWidth: "280px", // Smaller max width for compact design
                padding: "20px", // Reduced padding
                backgroundColor:
                  currentStep === index
                    ? "rgba(255, 255, 255, 0.95)"
                    : "rgba(255, 255, 255, 0.08)",
                borderRadius: "12px", // Smaller border radius
                backdropFilter: "blur(10px)",
                border:
                  currentStep === index
                    ? "2px solid #007AFF"
                    : "1px solid rgba(255, 255, 255, 0.15)",
                boxShadow:
                  currentStep === index
                    ? "0 15px 35px rgba(0, 122, 255, 0.25)"
                    : "0 8px 25px rgba(0, 0, 0, 0.15)",
              }}
            >
              <motion.div
                animate={{
                  color: currentStep === index ? "#000" : "#fff",
                }}
                transition={{ duration: 0.3 }}
              >
                <h3
                  style={{
                    margin: "0 0 8px 0", // Reduced margin
                    fontSize: "18px", // Smaller font size
                    fontWeight: "600",
                    fontFamily:
                      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                  }}
                >
                  {step.title}
                </h3>
                <p
                  style={{
                    margin: 0,
                    fontSize: "14px", // Smaller font size
                    lineHeight: "1.4", // Tighter line height
                    fontFamily:
                      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                  }}
                >
                  {step.description}
                </p>
              </motion.div>

              {/* Step indicator */}
              <motion.div
                style={{
                  position: "absolute",
                  top: "-8px",
                  right: "-8px",
                  width: "32px", // Smaller indicator
                  height: "32px",
                  borderRadius: "50%",
                  backgroundColor:
                    currentStep === index
                      ? "#007AFF"
                      : "rgba(255, 255, 255, 0.2)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  color: "#fff",
                  fontWeight: "bold",
                  fontSize: "14px", // Smaller font
                  fontFamily:
                    '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                }}
                animate={{
                  scale: currentStep === index ? 1.1 : 1,
                  backgroundColor:
                    currentStep === index
                      ? "#007AFF"
                      : "rgba(255, 255, 255, 0.2)",
                }}
                transition={{ duration: 0.3 }}
              >
                {step.id}
              </motion.div>
            </motion.div>
          ))}
        </div>

        {/* Progress indicator */}
        <motion.div
          style={{
            position: "fixed",
            bottom: "30px",
            left: "50%",
            transform: "translateX(-50%)",
            display: "flex",
            gap: "10px",
            zIndex: 20,
          }}
        >
          {instructionSteps.map((_, index) => (
            <motion.div
              key={index}
              style={{
                width: "12px",
                height: "12px",
                borderRadius: "50%",
                backgroundColor:
                  currentStep === index
                    ? "#007AFF"
                    : "rgba(255, 255, 255, 0.3)",
              }}
              animate={{
                scale: currentStep === index ? 1.3 : 1,
                backgroundColor:
                  currentStep === index
                    ? "#007AFF"
                    : "rgba(255, 255, 255, 0.3)",
              }}
              transition={{ duration: 0.3 }}
            />
          ))}
        </motion.div>
      </div>
    </div>
  );
}

export default App;
